// Generated from simplf.g4 by ANTLR 4.13.2

    package simplf.parser;
    import simplf.Stmt;
    import simplf.Stmt.*;

    import simplf.Expr;
    import simplf.Expr.*;

import org.antlr.v4.runtime.Lexer;
import org.antlr.v4.runtime.CharStream;
import org.antlr.v4.runtime.Token;
import org.antlr.v4.runtime.TokenStream;
import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.atn.*;
import org.antlr.v4.runtime.dfa.DFA;
import org.antlr.v4.runtime.misc.*;

@SuppressWarnings({"all", "warnings", "unchecked", "unused", "cast", "CheckReturnValue", "this-escape"})
public class simplfLexer extends Lexer {
	static { RuntimeMetaData.checkVersion("4.13.2", RuntimeMetaData.VERSION); }

	protected static final DFA[] _decisionToDFA;
	protected static final PredictionContextCache _sharedContextCache =
		new PredictionContextCache();
	public static final int
		LEFT_PAREN=1, RIGHT_PAREN=2, LEFT_BRACE=3, RIGHT_BRACE=4, COMMA=5, DOT=6, 
		MINUS=7, PLUS=8, SEMICOLON=9, STAR=10, SLASH=11, BANG=12, BANG_EQUAL=13, 
		EQUAL=14, EQUAL_EQUAL=15, GREATER=16, GREATER_EQUAL=17, LESS=18, LESS_EQUAL=19, 
		AND=20, CLASS=21, ELSE=22, FALSE=23, FOR=24, FUN=25, IF=26, NIL=27, OR=28, 
		PRINT=29, SUPER=30, THIS=31, TRUE=32, VAR=33, WHILE=34, IDENTIFIER=35, 
		STRINGLIT=36, NUMBER=37, COMMENT=38, WHITESPACE=39;
	public static String[] channelNames = {
		"DEFAULT_TOKEN_CHANNEL", "HIDDEN"
	};

	public static String[] modeNames = {
		"DEFAULT_MODE"
	};

	private static String[] makeRuleNames() {
		return new String[] {
			"LEFT_PAREN", "RIGHT_PAREN", "LEFT_BRACE", "RIGHT_BRACE", "COMMA", "DOT", 
			"MINUS", "PLUS", "SEMICOLON", "STAR", "SLASH", "BANG", "BANG_EQUAL", 
			"EQUAL", "EQUAL_EQUAL", "GREATER", "GREATER_EQUAL", "LESS", "LESS_EQUAL", 
			"AND", "CLASS", "ELSE", "FALSE", "FOR", "FUN", "IF", "NIL", "OR", "PRINT", 
			"SUPER", "THIS", "TRUE", "VAR", "WHILE", "IDENTIFIER", "STRINGLIT", "NUMBER", 
			"COMMENT", "WHITESPACE"
		};
	}
	public static final String[] ruleNames = makeRuleNames();

	private static String[] makeLiteralNames() {
		return new String[] {
			null, "'('", "')'", "'{'", "'}'", "','", "'.'", "'-'", "'+'", "';'", 
			"'*'", "'/'", "'!'", "'!='", "'='", "'=='", "'>'", "'>='", "'<'", "'<='", 
			"'and'", "'class'", "'else'", "'false'", "'for'", "'fun'", "'if'", "'nil'", 
			"'or'", "'print'", "'super'", "'this'", "'true'", "'var'", "'while'"
		};
	}
	private static final String[] _LITERAL_NAMES = makeLiteralNames();
	private static String[] makeSymbolicNames() {
		return new String[] {
			null, "LEFT_PAREN", "RIGHT_PAREN", "LEFT_BRACE", "RIGHT_BRACE", "COMMA", 
			"DOT", "MINUS", "PLUS", "SEMICOLON", "STAR", "SLASH", "BANG", "BANG_EQUAL", 
			"EQUAL", "EQUAL_EQUAL", "GREATER", "GREATER_EQUAL", "LESS", "LESS_EQUAL", 
			"AND", "CLASS", "ELSE", "FALSE", "FOR", "FUN", "IF", "NIL", "OR", "PRINT", 
			"SUPER", "THIS", "TRUE", "VAR", "WHILE", "IDENTIFIER", "STRINGLIT", "NUMBER", 
			"COMMENT", "WHITESPACE"
		};
	}
	private static final String[] _SYMBOLIC_NAMES = makeSymbolicNames();
	public static final Vocabulary VOCABULARY = new VocabularyImpl(_LITERAL_NAMES, _SYMBOLIC_NAMES);

	/**
	 * @deprecated Use {@link #VOCABULARY} instead.
	 */
	@Deprecated
	public static final String[] tokenNames;
	static {
		tokenNames = new String[_SYMBOLIC_NAMES.length];
		for (int i = 0; i < tokenNames.length; i++) {
			tokenNames[i] = VOCABULARY.getLiteralName(i);
			if (tokenNames[i] == null) {
				tokenNames[i] = VOCABULARY.getSymbolicName(i);
			}

			if (tokenNames[i] == null) {
				tokenNames[i] = "<INVALID>";
			}
		}
	}

	@Override
	@Deprecated
	public String[] getTokenNames() {
		return tokenNames;
	}

	@Override

	public Vocabulary getVocabulary() {
		return VOCABULARY;
	}


	public simplfLexer(CharStream input) {
		super(input);
		_interp = new LexerATNSimulator(this,_ATN,_decisionToDFA,_sharedContextCache);
	}

	@Override
	public String getGrammarFileName() { return "simplf.g4"; }

	@Override
	public String[] getRuleNames() { return ruleNames; }

	@Override
	public String getSerializedATN() { return _serializedATN; }

	@Override
	public String[] getChannelNames() { return channelNames; }

	@Override
	public String[] getModeNames() { return modeNames; }

	@Override
	public ATN getATN() { return _ATN; }

	public static final String _serializedATN =
		"\u0004\u0000\'\u00f6\u0006\uffff\uffff\u0002\u0000\u0007\u0000\u0002\u0001"+
		"\u0007\u0001\u0002\u0002\u0007\u0002\u0002\u0003\u0007\u0003\u0002\u0004"+
		"\u0007\u0004\u0002\u0005\u0007\u0005\u0002\u0006\u0007\u0006\u0002\u0007"+
		"\u0007\u0007\u0002\b\u0007\b\u0002\t\u0007\t\u0002\n\u0007\n\u0002\u000b"+
		"\u0007\u000b\u0002\f\u0007\f\u0002\r\u0007\r\u0002\u000e\u0007\u000e\u0002"+
		"\u000f\u0007\u000f\u0002\u0010\u0007\u0010\u0002\u0011\u0007\u0011\u0002"+
		"\u0012\u0007\u0012\u0002\u0013\u0007\u0013\u0002\u0014\u0007\u0014\u0002"+
		"\u0015\u0007\u0015\u0002\u0016\u0007\u0016\u0002\u0017\u0007\u0017\u0002"+
		"\u0018\u0007\u0018\u0002\u0019\u0007\u0019\u0002\u001a\u0007\u001a\u0002"+
		"\u001b\u0007\u001b\u0002\u001c\u0007\u001c\u0002\u001d\u0007\u001d\u0002"+
		"\u001e\u0007\u001e\u0002\u001f\u0007\u001f\u0002 \u0007 \u0002!\u0007"+
		"!\u0002\"\u0007\"\u0002#\u0007#\u0002$\u0007$\u0002%\u0007%\u0002&\u0007"+
		"&\u0001\u0000\u0001\u0000\u0001\u0001\u0001\u0001\u0001\u0002\u0001\u0002"+
		"\u0001\u0003\u0001\u0003\u0001\u0004\u0001\u0004\u0001\u0005\u0001\u0005"+
		"\u0001\u0006\u0001\u0006\u0001\u0007\u0001\u0007\u0001\b\u0001\b\u0001"+
		"\t\u0001\t\u0001\n\u0001\n\u0001\u000b\u0001\u000b\u0001\f\u0001\f\u0001"+
		"\f\u0001\r\u0001\r\u0001\u000e\u0001\u000e\u0001\u000e\u0001\u000f\u0001"+
		"\u000f\u0001\u0010\u0001\u0010\u0001\u0010\u0001\u0011\u0001\u0011\u0001"+
		"\u0012\u0001\u0012\u0001\u0012\u0001\u0013\u0001\u0013\u0001\u0013\u0001"+
		"\u0013\u0001\u0014\u0001\u0014\u0001\u0014\u0001\u0014\u0001\u0014\u0001"+
		"\u0014\u0001\u0015\u0001\u0015\u0001\u0015\u0001\u0015\u0001\u0015\u0001"+
		"\u0016\u0001\u0016\u0001\u0016\u0001\u0016\u0001\u0016\u0001\u0016\u0001"+
		"\u0017\u0001\u0017\u0001\u0017\u0001\u0017\u0001\u0018\u0001\u0018\u0001"+
		"\u0018\u0001\u0018\u0001\u0019\u0001\u0019\u0001\u0019\u0001\u001a\u0001"+
		"\u001a\u0001\u001a\u0001\u001a\u0001\u001b\u0001\u001b\u0001\u001b\u0001"+
		"\u001c\u0001\u001c\u0001\u001c\u0001\u001c\u0001\u001c\u0001\u001c\u0001"+
		"\u001d\u0001\u001d\u0001\u001d\u0001\u001d\u0001\u001d\u0001\u001d\u0001"+
		"\u001e\u0001\u001e\u0001\u001e\u0001\u001e\u0001\u001e\u0001\u001f\u0001"+
		"\u001f\u0001\u001f\u0001\u001f\u0001\u001f\u0001 \u0001 \u0001 \u0001"+
		" \u0001!\u0001!\u0001!\u0001!\u0001!\u0001!\u0001\"\u0001\"\u0005\"\u00c3"+
		"\b\"\n\"\f\"\u00c6\t\"\u0001#\u0001#\u0005#\u00ca\b#\n#\f#\u00cd\t#\u0001"+
		"#\u0001#\u0001$\u0004$\u00d2\b$\u000b$\f$\u00d3\u0001$\u0004$\u00d7\b"+
		"$\u000b$\f$\u00d8\u0001$\u0001$\u0004$\u00dd\b$\u000b$\f$\u00de\u0003"+
		"$\u00e1\b$\u0001%\u0001%\u0001%\u0001%\u0005%\u00e7\b%\n%\f%\u00ea\t%"+
		"\u0001%\u0001%\u0001%\u0001%\u0001&\u0004&\u00f1\b&\u000b&\f&\u00f2\u0001"+
		"&\u0001&\u0000\u0000\'\u0001\u0001\u0003\u0002\u0005\u0003\u0007\u0004"+
		"\t\u0005\u000b\u0006\r\u0007\u000f\b\u0011\t\u0013\n\u0015\u000b\u0017"+
		"\f\u0019\r\u001b\u000e\u001d\u000f\u001f\u0010!\u0011#\u0012%\u0013\'"+
		"\u0014)\u0015+\u0016-\u0017/\u00181\u00193\u001a5\u001b7\u001c9\u001d"+
		";\u001e=\u001f? A!C\"E#G$I%K&M\'\u0001\u0000\u0006\u0003\u0000AZ__az\u0004"+
		"\u000009AZ__az\u0003\u0000\n\n\r\r\"\"\u0001\u000009\u0001\u0000\n\n\u0003"+
		"\u0000\t\n\r\r  \u00fd\u0000\u0001\u0001\u0000\u0000\u0000\u0000\u0003"+
		"\u0001\u0000\u0000\u0000\u0000\u0005\u0001\u0000\u0000\u0000\u0000\u0007"+
		"\u0001\u0000\u0000\u0000\u0000\t\u0001\u0000\u0000\u0000\u0000\u000b\u0001"+
		"\u0000\u0000\u0000\u0000\r\u0001\u0000\u0000\u0000\u0000\u000f\u0001\u0000"+
		"\u0000\u0000\u0000\u0011\u0001\u0000\u0000\u0000\u0000\u0013\u0001\u0000"+
		"\u0000\u0000\u0000\u0015\u0001\u0000\u0000\u0000\u0000\u0017\u0001\u0000"+
		"\u0000\u0000\u0000\u0019\u0001\u0000\u0000\u0000\u0000\u001b\u0001\u0000"+
		"\u0000\u0000\u0000\u001d\u0001\u0000\u0000\u0000\u0000\u001f\u0001\u0000"+
		"\u0000\u0000\u0000!\u0001\u0000\u0000\u0000\u0000#\u0001\u0000\u0000\u0000"+
		"\u0000%\u0001\u0000\u0000\u0000\u0000\'\u0001\u0000\u0000\u0000\u0000"+
		")\u0001\u0000\u0000\u0000\u0000+\u0001\u0000\u0000\u0000\u0000-\u0001"+
		"\u0000\u0000\u0000\u0000/\u0001\u0000\u0000\u0000\u00001\u0001\u0000\u0000"+
		"\u0000\u00003\u0001\u0000\u0000\u0000\u00005\u0001\u0000\u0000\u0000\u0000"+
		"7\u0001\u0000\u0000\u0000\u00009\u0001\u0000\u0000\u0000\u0000;\u0001"+
		"\u0000\u0000\u0000\u0000=\u0001\u0000\u0000\u0000\u0000?\u0001\u0000\u0000"+
		"\u0000\u0000A\u0001\u0000\u0000\u0000\u0000C\u0001\u0000\u0000\u0000\u0000"+
		"E\u0001\u0000\u0000\u0000\u0000G\u0001\u0000\u0000\u0000\u0000I\u0001"+
		"\u0000\u0000\u0000\u0000K\u0001\u0000\u0000\u0000\u0000M\u0001\u0000\u0000"+
		"\u0000\u0001O\u0001\u0000\u0000\u0000\u0003Q\u0001\u0000\u0000\u0000\u0005"+
		"S\u0001\u0000\u0000\u0000\u0007U\u0001\u0000\u0000\u0000\tW\u0001\u0000"+
		"\u0000\u0000\u000bY\u0001\u0000\u0000\u0000\r[\u0001\u0000\u0000\u0000"+
		"\u000f]\u0001\u0000\u0000\u0000\u0011_\u0001\u0000\u0000\u0000\u0013a"+
		"\u0001\u0000\u0000\u0000\u0015c\u0001\u0000\u0000\u0000\u0017e\u0001\u0000"+
		"\u0000\u0000\u0019g\u0001\u0000\u0000\u0000\u001bj\u0001\u0000\u0000\u0000"+
		"\u001dl\u0001\u0000\u0000\u0000\u001fo\u0001\u0000\u0000\u0000!q\u0001"+
		"\u0000\u0000\u0000#t\u0001\u0000\u0000\u0000%v\u0001\u0000\u0000\u0000"+
		"\'y\u0001\u0000\u0000\u0000)}\u0001\u0000\u0000\u0000+\u0083\u0001\u0000"+
		"\u0000\u0000-\u0088\u0001\u0000\u0000\u0000/\u008e\u0001\u0000\u0000\u0000"+
		"1\u0092\u0001\u0000\u0000\u00003\u0096\u0001\u0000\u0000\u00005\u0099"+
		"\u0001\u0000\u0000\u00007\u009d\u0001\u0000\u0000\u00009\u00a0\u0001\u0000"+
		"\u0000\u0000;\u00a6\u0001\u0000\u0000\u0000=\u00ac\u0001\u0000\u0000\u0000"+
		"?\u00b1\u0001\u0000\u0000\u0000A\u00b6\u0001\u0000\u0000\u0000C\u00ba"+
		"\u0001\u0000\u0000\u0000E\u00c0\u0001\u0000\u0000\u0000G\u00c7\u0001\u0000"+
		"\u0000\u0000I\u00e0\u0001\u0000\u0000\u0000K\u00e2\u0001\u0000\u0000\u0000"+
		"M\u00f0\u0001\u0000\u0000\u0000OP\u0005(\u0000\u0000P\u0002\u0001\u0000"+
		"\u0000\u0000QR\u0005)\u0000\u0000R\u0004\u0001\u0000\u0000\u0000ST\u0005"+
		"{\u0000\u0000T\u0006\u0001\u0000\u0000\u0000UV\u0005}\u0000\u0000V\b\u0001"+
		"\u0000\u0000\u0000WX\u0005,\u0000\u0000X\n\u0001\u0000\u0000\u0000YZ\u0005"+
		".\u0000\u0000Z\f\u0001\u0000\u0000\u0000[\\\u0005-\u0000\u0000\\\u000e"+
		"\u0001\u0000\u0000\u0000]^\u0005+\u0000\u0000^\u0010\u0001\u0000\u0000"+
		"\u0000_`\u0005;\u0000\u0000`\u0012\u0001\u0000\u0000\u0000ab\u0005*\u0000"+
		"\u0000b\u0014\u0001\u0000\u0000\u0000cd\u0005/\u0000\u0000d\u0016\u0001"+
		"\u0000\u0000\u0000ef\u0005!\u0000\u0000f\u0018\u0001\u0000\u0000\u0000"+
		"gh\u0005!\u0000\u0000hi\u0005=\u0000\u0000i\u001a\u0001\u0000\u0000\u0000"+
		"jk\u0005=\u0000\u0000k\u001c\u0001\u0000\u0000\u0000lm\u0005=\u0000\u0000"+
		"mn\u0005=\u0000\u0000n\u001e\u0001\u0000\u0000\u0000op\u0005>\u0000\u0000"+
		"p \u0001\u0000\u0000\u0000qr\u0005>\u0000\u0000rs\u0005=\u0000\u0000s"+
		"\"\u0001\u0000\u0000\u0000tu\u0005<\u0000\u0000u$\u0001\u0000\u0000\u0000"+
		"vw\u0005<\u0000\u0000wx\u0005=\u0000\u0000x&\u0001\u0000\u0000\u0000y"+
		"z\u0005a\u0000\u0000z{\u0005n\u0000\u0000{|\u0005d\u0000\u0000|(\u0001"+
		"\u0000\u0000\u0000}~\u0005c\u0000\u0000~\u007f\u0005l\u0000\u0000\u007f"+
		"\u0080\u0005a\u0000\u0000\u0080\u0081\u0005s\u0000\u0000\u0081\u0082\u0005"+
		"s\u0000\u0000\u0082*\u0001\u0000\u0000\u0000\u0083\u0084\u0005e\u0000"+
		"\u0000\u0084\u0085\u0005l\u0000\u0000\u0085\u0086\u0005s\u0000\u0000\u0086"+
		"\u0087\u0005e\u0000\u0000\u0087,\u0001\u0000\u0000\u0000\u0088\u0089\u0005"+
		"f\u0000\u0000\u0089\u008a\u0005a\u0000\u0000\u008a\u008b\u0005l\u0000"+
		"\u0000\u008b\u008c\u0005s\u0000\u0000\u008c\u008d\u0005e\u0000\u0000\u008d"+
		".\u0001\u0000\u0000\u0000\u008e\u008f\u0005f\u0000\u0000\u008f\u0090\u0005"+
		"o\u0000\u0000\u0090\u0091\u0005r\u0000\u0000\u00910\u0001\u0000\u0000"+
		"\u0000\u0092\u0093\u0005f\u0000\u0000\u0093\u0094\u0005u\u0000\u0000\u0094"+
		"\u0095\u0005n\u0000\u0000\u00952\u0001\u0000\u0000\u0000\u0096\u0097\u0005"+
		"i\u0000\u0000\u0097\u0098\u0005f\u0000\u0000\u00984\u0001\u0000\u0000"+
		"\u0000\u0099\u009a\u0005n\u0000\u0000\u009a\u009b\u0005i\u0000\u0000\u009b"+
		"\u009c\u0005l\u0000\u0000\u009c6\u0001\u0000\u0000\u0000\u009d\u009e\u0005"+
		"o\u0000\u0000\u009e\u009f\u0005r\u0000\u0000\u009f8\u0001\u0000\u0000"+
		"\u0000\u00a0\u00a1\u0005p\u0000\u0000\u00a1\u00a2\u0005r\u0000\u0000\u00a2"+
		"\u00a3\u0005i\u0000\u0000\u00a3\u00a4\u0005n\u0000\u0000\u00a4\u00a5\u0005"+
		"t\u0000\u0000\u00a5:\u0001\u0000\u0000\u0000\u00a6\u00a7\u0005s\u0000"+
		"\u0000\u00a7\u00a8\u0005u\u0000\u0000\u00a8\u00a9\u0005p\u0000\u0000\u00a9"+
		"\u00aa\u0005e\u0000\u0000\u00aa\u00ab\u0005r\u0000\u0000\u00ab<\u0001"+
		"\u0000\u0000\u0000\u00ac\u00ad\u0005t\u0000\u0000\u00ad\u00ae\u0005h\u0000"+
		"\u0000\u00ae\u00af\u0005i\u0000\u0000\u00af\u00b0\u0005s\u0000\u0000\u00b0"+
		">\u0001\u0000\u0000\u0000\u00b1\u00b2\u0005t\u0000\u0000\u00b2\u00b3\u0005"+
		"r\u0000\u0000\u00b3\u00b4\u0005u\u0000\u0000\u00b4\u00b5\u0005e\u0000"+
		"\u0000\u00b5@\u0001\u0000\u0000\u0000\u00b6\u00b7\u0005v\u0000\u0000\u00b7"+
		"\u00b8\u0005a\u0000\u0000\u00b8\u00b9\u0005r\u0000\u0000\u00b9B\u0001"+
		"\u0000\u0000\u0000\u00ba\u00bb\u0005w\u0000\u0000\u00bb\u00bc\u0005h\u0000"+
		"\u0000\u00bc\u00bd\u0005i\u0000\u0000\u00bd\u00be\u0005l\u0000\u0000\u00be"+
		"\u00bf\u0005e\u0000\u0000\u00bfD\u0001\u0000\u0000\u0000\u00c0\u00c4\u0007"+
		"\u0000\u0000\u0000\u00c1\u00c3\u0007\u0001\u0000\u0000\u00c2\u00c1\u0001"+
		"\u0000\u0000\u0000\u00c3\u00c6\u0001\u0000\u0000\u0000\u00c4\u00c2\u0001"+
		"\u0000\u0000\u0000\u00c4\u00c5\u0001\u0000\u0000\u0000\u00c5F\u0001\u0000"+
		"\u0000\u0000\u00c6\u00c4\u0001\u0000\u0000\u0000\u00c7\u00cb\u0005\"\u0000"+
		"\u0000\u00c8\u00ca\b\u0002\u0000\u0000\u00c9\u00c8\u0001\u0000\u0000\u0000"+
		"\u00ca\u00cd\u0001\u0000\u0000\u0000\u00cb\u00c9\u0001\u0000\u0000\u0000"+
		"\u00cb\u00cc\u0001\u0000\u0000\u0000\u00cc\u00ce\u0001\u0000\u0000\u0000"+
		"\u00cd\u00cb\u0001\u0000\u0000\u0000\u00ce\u00cf\u0005\"\u0000\u0000\u00cf"+
		"H\u0001\u0000\u0000\u0000\u00d0\u00d2\u0007\u0003\u0000\u0000\u00d1\u00d0"+
		"\u0001\u0000\u0000\u0000\u00d2\u00d3\u0001\u0000\u0000\u0000\u00d3\u00d1"+
		"\u0001\u0000\u0000\u0000\u00d3\u00d4\u0001\u0000\u0000\u0000\u00d4\u00e1"+
		"\u0001\u0000\u0000\u0000\u00d5\u00d7\u0007\u0003\u0000\u0000\u00d6\u00d5"+
		"\u0001\u0000\u0000\u0000\u00d7\u00d8\u0001\u0000\u0000\u0000\u00d8\u00d6"+
		"\u0001\u0000\u0000\u0000\u00d8\u00d9\u0001\u0000\u0000\u0000\u00d9\u00da"+
		"\u0001\u0000\u0000\u0000\u00da\u00dc\u0005.\u0000\u0000\u00db\u00dd\u0007"+
		"\u0003\u0000\u0000\u00dc\u00db\u0001\u0000\u0000\u0000\u00dd\u00de\u0001"+
		"\u0000\u0000\u0000\u00de\u00dc\u0001\u0000\u0000\u0000\u00de\u00df\u0001"+
		"\u0000\u0000\u0000\u00df\u00e1\u0001\u0000\u0000\u0000\u00e0\u00d1\u0001"+
		"\u0000\u0000\u0000\u00e0\u00d6\u0001\u0000\u0000\u0000\u00e1J\u0001\u0000"+
		"\u0000\u0000\u00e2\u00e3\u0005/\u0000\u0000\u00e3\u00e4\u0005/\u0000\u0000"+
		"\u00e4\u00e8\u0001\u0000\u0000\u0000\u00e5\u00e7\b\u0004\u0000\u0000\u00e6"+
		"\u00e5\u0001\u0000\u0000\u0000\u00e7\u00ea\u0001\u0000\u0000\u0000\u00e8"+
		"\u00e6\u0001\u0000\u0000\u0000\u00e8\u00e9\u0001\u0000\u0000\u0000\u00e9"+
		"\u00eb\u0001\u0000\u0000\u0000\u00ea\u00e8\u0001\u0000\u0000\u0000\u00eb"+
		"\u00ec\u0005\n\u0000\u0000\u00ec\u00ed\u0001\u0000\u0000\u0000\u00ed\u00ee"+
		"\u0006%\u0000\u0000\u00eeL\u0001\u0000\u0000\u0000\u00ef\u00f1\u0007\u0005"+
		"\u0000\u0000\u00f0\u00ef\u0001\u0000\u0000\u0000\u00f1\u00f2\u0001\u0000"+
		"\u0000\u0000\u00f2\u00f0\u0001\u0000\u0000\u0000\u00f2\u00f3\u0001\u0000"+
		"\u0000\u0000\u00f3\u00f4\u0001\u0000\u0000\u0000\u00f4\u00f5\u0006&\u0000"+
		"\u0000\u00f5N\u0001\u0000\u0000\u0000\t\u0000\u00c4\u00cb\u00d3\u00d8"+
		"\u00de\u00e0\u00e8\u00f2\u0001\u0006\u0000\u0000";
	public static final ATN _ATN =
		new ATNDeserializer().deserialize(_serializedATN.toCharArray());
	static {
		_decisionToDFA = new DFA[_ATN.getNumberOfDecisions()];
		for (int i = 0; i < _ATN.getNumberOfDecisions(); i++) {
			_decisionToDFA[i] = new DFA(_ATN.getDecisionState(i), i);
		}
	}
}